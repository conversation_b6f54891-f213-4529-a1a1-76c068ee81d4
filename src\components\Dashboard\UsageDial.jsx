import React, { useEffect, useRef } from 'react'
import { Doughnut } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  ArcElement,
  Toolt<PERSON>,
  Legend,
} from 'chart.js'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt, HiTrendingDown, HiChartBar, HiTrendingUp } from 'react-icons/hi'

ChartJS.register(ArcElement, Tooltip, Legend)

function UsageDial() {
  const { state, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme } = useTheme()
  const chartRef = useRef(null)

  const remainingUnits = state.currentUnits
  const usedUnits = usageSinceLastRecording
  const totalUnits = remainingUnits + usedUnits
  const usagePercentage = totalUnits > 0 ? (usedUnits / totalUnits) * 100 : 0

  // Function to create responsive gradients
  const createGradients = () => {
    const chart = chartRef.current
    if (chart && chart.ctx && chart.canvas) {
      const ctx = chart.ctx
      const canvas = chart.canvas

      // Get canvas dimensions for responsive gradients
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2
      const innerRadius = Math.min(centerX, centerY) * 0.2
      const outerRadius = Math.min(centerX, centerY) * 0.8

      // Advanced gradient for remaining units (vibrant blue to purple)
      const remainingGradient = ctx.createRadialGradient(centerX, centerY, innerRadius, centerX, centerY, outerRadius)
      remainingGradient.addColorStop(0, '#667eea') // Light purple
      remainingGradient.addColorStop(0.3, '#764ba2') // Medium purple
      remainingGradient.addColorStop(0.6, '#667eea') // Light purple
      remainingGradient.addColorStop(1, '#f093fb') // Pink

      // Advanced gradient for used units (warm orange to red)
      const usedGradient = ctx.createRadialGradient(centerX, centerY, innerRadius, centerX, centerY, outerRadius)
      usedGradient.addColorStop(0, '#ff9a9e') // Light coral
      usedGradient.addColorStop(0.3, '#fecfef') // Light pink
      usedGradient.addColorStop(0.6, '#fecfef') // Light pink
      usedGradient.addColorStop(1, '#ffc3a0') // Peach

      chart.data.datasets[0].backgroundColor = [remainingGradient, usedGradient]
      chart.update()
    }
  }

  // Create gradients when data changes
  useEffect(() => {
    createGradients()
  }, [remainingUnits, usedUnits])

  // Also create gradients after a short delay to ensure canvas is ready
  useEffect(() => {
    const timer = setTimeout(() => {
      createGradients()
    }, 100)
    return () => clearTimeout(timer)
  }, [])

  const data = {
    labels: [`Remaining ${getDisplayUnitName()}`, `Used ${getDisplayUnitName()}`],
    datasets: [
      {
        data: [remainingUnits, usedUnits],
        backgroundColor: [
          'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Modern gradient fallback
          'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)',
        ],
        borderColor: [
          'rgba(255, 255, 255, 0.9)',
          'rgba(255, 255, 255, 0.9)',
        ],
        borderWidth: 4,
        cutout: '78%',
        borderRadius: 12,
        borderJoinStyle: 'round',
        hoverBorderWidth: 6,
        hoverBorderColor: [
          'rgba(255, 255, 255, 1)',
          'rgba(255, 255, 255, 1)',
        ],
        shadowOffsetX: 3,
        shadowOffsetY: 3,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    onResize: (chart) => {
      // Recreate gradients when chart is resized
      setTimeout(() => createGradients(), 50)
    },
    plugins: {
      legend: {
        display: false, // We'll create custom legend
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.parsed
            const percentage = totalUnits > 0 ? ((value / totalUnits) * 100).toFixed(1) : 0
            return `${label}: ${value.toFixed(2)} (${percentage}%)`
          }
        }
      }
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
      easing: 'easeInOutCubic',
      delay: (context) => context.dataIndex * 200,
      onComplete: () => {
        // Ensure gradients are applied after animation completes
        setTimeout(() => createGradients(), 100)
      }
    },
    interaction: {
      intersect: false,
      mode: 'nearest',
    },
    elements: {
      arc: {
        borderWidth: 4,
        hoverBorderWidth: 6,
        borderSkipped: false,
        borderAlign: 'inner',
      }
    },
    layout: {
      padding: {
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }
    }
  }

  return (
    <div className="relative">
      {/* MAIN FEATURE: Large Dial Without Card Container */}
      <div className="relative h-[32rem] mb-8 flex items-center justify-center">
        {/* Floating orbs for ambient effect */}
        <div className={`absolute top-8 left-8 w-20 h-20 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-xl animate-pulse`}></div>
        <div className={`absolute bottom-8 right-8 w-24 h-24 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-2xl animate-pulse`} style={{animationDelay: '1s'}}></div>
        <div className={`absolute top-1/2 left-4 w-16 h-16 bg-gradient-to-r ${theme.gradient} rounded-full opacity-10 blur-lg animate-pulse`} style={{animationDelay: '2s'}}></div>
        <div className={`absolute top-1/4 right-8 w-12 h-12 bg-gradient-to-r ${theme.gradient} rounded-full opacity-25 blur-md animate-pulse`} style={{animationDelay: '0.5s'}}></div>

        {/* Large Chart Container */}
        <div className="relative h-full w-full max-w-lg max-h-lg flex items-center justify-center">
          <Doughnut ref={chartRef} data={data} options={options} />

          {/* HERO CENTER CONTENT - Main Feature */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              {/* Massive outer glow ring */}
              <div className="absolute inset-0 w-64 h-64 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-2xl opacity-40 animate-pulse"></div>

              {/* Hero circular container */}
              <div className={`relative w-64 h-64 ${theme.card}/50 border-4 ${theme.border}/40 backdrop-blur-xl rounded-full shadow-2xl flex items-center justify-center`}>
                {/* Inner gradient ring */}
                <div className={`absolute inset-3 ${theme.secondary}/70 rounded-full`}></div>

                {/* Hero content container */}
                <div className="relative text-center z-10">
                  {/* Properly sized animated icon */}
                  <div className="mb-2 flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"></div>
                      <HiLightningBolt className="relative h-8 w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce" style={{animationDuration: '2s'}} />
                    </div>
                  </div>

                  {/* Properly sized main value */}
                  <div className="relative mb-2">
                    <div className={`text-4xl font-black ${theme.text} drop-shadow-lg`}>
                      {remainingUnits.toFixed(2)}
                    </div>
                    <div className={`text-sm font-bold ${theme.textSecondary} mt-1 tracking-wide`}>
                      {getDisplayUnitName()} Left
                    </div>
                  </div>

                  {/* Properly sized usage percentage */}
                  <div className="mt-1">
                    <div className={`text-base font-bold tracking-tight ${theme.textSecondary} drop-shadow-lg`}>
                      {usagePercentage.toFixed(1)}% Used
                    </div>
                  </div>
                </div>

                {/* Enhanced decorative elements */}
                <div className="absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-70 animate-pulse"></div>
                <div className="absolute bottom-6 left-6 w-2 h-2 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-60 animate-pulse" style={{animationDelay: '1s'}}></div>
                <div className="absolute top-1/4 right-8 w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1.5s'}}></div>
                <div className="absolute bottom-1/4 left-8 w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40 animate-pulse" style={{animationDelay: '0.5s'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Information Cards */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Current Units Card - Compact */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-4 border ${theme.border}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-base font-semibold mb-1 ${theme.text}`}>Current Units</h3>
              <p className={`text-2xl font-bold ${theme.text}`}>
                {state.currentUnits.toFixed(2)}
              </p>
              <p className={`text-xs font-medium ${theme.textSecondary}`}>{getDisplayUnitName()} remaining</p>
              <p className={`text-sm ${theme.textSecondary} font-semibold mt-1`}>
                Value: {state.currencySymbol}{(state.currentUnits * state.unitCost).toFixed(2)}
              </p>
            </div>
            <div className={`p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
              <HiLightningBolt className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        {/* Usage Since Last Recording Card - Compact */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-4 border ${theme.border}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-base font-semibold mb-1 ${theme.text}`}>Usage Since Last Recording</h3>
              <p className={`text-2xl font-bold ${theme.text}`}>
                {usageSinceLastRecording.toFixed(2)}
              </p>
              <p className={`text-xs font-medium ${theme.textSecondary}`}>{getDisplayUnitName()} used</p>
              <p className={`text-sm ${theme.textSecondary} font-semibold mt-1`}>
                Cost: {state.currencySymbol}{(usageSinceLastRecording * state.unitCost).toFixed(2)}
              </p>
            </div>
            <div className={`p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
              <HiTrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Compact Cost Information Cards */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Total Cost Card - Compact */}
        <div className={`relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-4 shadow-lg`}>
          <div className={`absolute inset-0 ${theme.secondary}`}></div>
          <div className="relative">
            <div className="flex items-center justify-between">
              <div>
                <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-1`}>Total Cost</div>
                <div className={`text-2xl font-black ${theme.text}`}>
                  {state.currencySymbol || 'R'}{(usedUnits * state.unitCost).toFixed(2)}
                </div>
                <div className={`text-xs font-medium ${theme.textSecondary} mt-1`}>
                  For {usedUnits.toFixed(2)} {getDisplayUnitName()} used
                </div>
              </div>
              <div className={`p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                <HiChartBar className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
          {/* Decorative elements */}
          <div className={`absolute top-1 right-1 w-6 h-6 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
          <div className={`absolute bottom-1 left-1 w-4 h-4 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
        </div>

        {/* Rate Card - Compact */}
        <div className={`relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-4 shadow-lg`}>
          <div className={`absolute inset-0 ${theme.secondary}`}></div>
          <div className="relative">
            <div className="flex items-center justify-between">
              <div>
                <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-1`}>Current Rate</div>
                <div className={`text-2xl font-black ${theme.text}`}>
                  {state.currencySymbol || 'R'}{state.unitCost.toFixed(2)}
                </div>
                <div className={`text-xs font-medium ${theme.textSecondary} mt-1`}>
                  Per {getDisplayUnitName()}
                </div>
              </div>
              <div className={`p-3 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                <HiTrendingDown className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
          {/* Decorative elements */}
          <div className={`absolute top-1 right-1 w-4 h-4 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
          <div className={`absolute bottom-1 left-1 w-6 h-6 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
        </div>
      </div>

      {/* Modern Progress Bar with Enhanced Styling */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <span className={`text-sm font-bold ${theme.textSecondary} tracking-wide uppercase`}>Usage Progress</span>
          <span className={`text-lg font-black px-3 py-1 rounded-full ${theme.secondary} ${theme.text}`}>
            {usagePercentage.toFixed(1)}%
          </span>
        </div>
        <div className="relative">
          {/* Background track */}
          <div className={`w-full ${theme.secondary} rounded-full h-4 border ${theme.border}`}>
            {/* Progress fill - clean and crisp */}
            <div
              className={`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${
                usagePercentage > 70 ? 'from-red-500 to-red-600' :
                usagePercentage > 40 ? 'from-amber-500 to-orange-600' :
                'from-emerald-500 to-green-600'
              }`}
              style={{
                width: `${Math.min(usagePercentage, 100)}%`
              }}
            />
          </div>


        </div>
      </div>
    </div>
  )
}

export default UsageDial
